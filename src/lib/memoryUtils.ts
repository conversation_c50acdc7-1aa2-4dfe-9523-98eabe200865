/**
 * Utility functions for memory management and optimization
 */

import logger from '@/lib/logger';

/**
 * Memory usage statistics
 */
export interface MemoryStats {
  /** Total heap size in bytes */
  heapTotal: number;
  /** Used heap size in bytes */
  heapUsed: number;
  /** External memory size in bytes */
  external: number;
  /** RSS (Resident Set Size) in bytes */
  rss: number;
  /** Array buffers size in bytes */
  arrayBuffers: number;
  /** Heap usage percentage */
  heapUsagePercentage: number;
}

/**
 * Get current memory usage statistics
 * @returns Memory usage statistics
 */
export function getMemoryStats(): MemoryStats {
  // Get memory usage from Node.js process
  const memoryUsage = process.memoryUsage();
  
  // Calculate heap usage percentage
  const heapUsagePercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
  
  return {
    heapTotal: memoryUsage.heapTotal,
    heapUsed: memoryUsage.heapUsed,
    external: memoryUsage.external,
    rss: memoryUsage.rss,
    arrayBuffers: memoryUsage.arrayBuffers || 0,
    heapUsagePercentage,
  };
}

/**
 * Format bytes to a human-readable string
 * @param bytes Bytes to format
 * @param decimals Number of decimal places
 * @returns Formatted string
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Log current memory usage
 * @param label Optional label for the log
 */
export function logMemoryUsage(label = 'Memory Usage'): void {
  const stats = getMemoryStats();
  
  logger.info(`${label}`, {
    heapTotal: formatBytes(stats.heapTotal),
    heapUsed: formatBytes(stats.heapUsed),
    external: formatBytes(stats.external),
    rss: formatBytes(stats.rss),
    arrayBuffers: formatBytes(stats.arrayBuffers),
    heapUsagePercentage: `${stats.heapUsagePercentage.toFixed(2)}%`,
  });
}

/**
 * Check if memory usage is above a threshold
 * @param thresholdPercentage Threshold percentage (0-100)
 * @returns True if memory usage is above the threshold
 */
export function isMemoryUsageHigh(thresholdPercentage = 85): boolean {
  const stats = getMemoryStats();
  return stats.heapUsagePercentage > thresholdPercentage;
}

/**
 * Attempt to free memory by forcing garbage collection
 * Note: This is not guaranteed to work and depends on the Node.js version and flags
 */
export function attemptGarbageCollection(): void {
  if (global.gc) {
    logger.info('Forcing garbage collection');
    global.gc();
    logMemoryUsage('Memory usage after garbage collection');
  } else {
    logger.warn('Cannot force garbage collection', null, {
      reason: 'Node.js was not started with --expose-gc flag'
    });
  }
}

/**
 * Stream processor for handling large data
 * @param iterator Async iterator that produces data
 * @param processor Function to process each chunk
 * @param options Options for streaming
 * @returns Promise that resolves when all data is processed
 */
export async function streamProcess<T, R>(
  iterator: AsyncIterable<T>,
  processor: (chunk: T) => Promise<R>,
  options: {
    onProgress?: (processed: number, total?: number) => void;
    batchSize?: number;
    memoryCheckInterval?: number;
    memoryThreshold?: number;
  } = {}
): Promise<R[]> {
  const {
    onProgress,
    batchSize = 100,
    memoryCheckInterval = 1000,
    memoryThreshold = 85,
  } = options;
  
  const results: R[] = [];
  let batch: T[] = [];
  let processed = 0;
  let lastMemoryCheck = Date.now();
  
  for await (const item of iterator) {
    batch.push(item);
    
    // Process in batches to avoid memory pressure
    if (batch.length >= batchSize) {
      const batchResults = await Promise.all(batch.map(processor));
      results.push(...batchResults);
      processed += batch.length;
      batch = [];
      
      // Report progress if callback provided
      if (onProgress) {
        onProgress(processed);
      }
      
      // Check memory usage periodically
      const now = Date.now();
      if (now - lastMemoryCheck > memoryCheckInterval) {
        lastMemoryCheck = now;
        
        if (isMemoryUsageHigh(memoryThreshold)) {
          logger.warn('High memory usage detected during streaming', null, {
            processed,
            memoryUsage: getMemoryStats().heapUsagePercentage.toFixed(2) + '%'
          });
          
          // Pause processing to allow garbage collection
          await new Promise(resolve => setTimeout(resolve, 1000));
          attemptGarbageCollection();
        }
      }
    }
  }
  
  // Process remaining items
  if (batch.length > 0) {
    const batchResults = await Promise.all(batch.map(processor));
    results.push(...batchResults);
    processed += batch.length;
    
    if (onProgress) {
      onProgress(processed);
    }
  }
  
  return results;
}

/**
 * Create a readable stream from an array
 * @param array Array to stream
 * @param options Options for streaming
 * @returns Async generator that yields array items
 */
export async function* createArrayStream<T>(
  array: T[],
  options: {
    chunkSize?: number;
    delay?: number;
  } = {}
): AsyncGenerator<T> {
  const { chunkSize = 100, delay = 0 } = options;
  
  for (let i = 0; i < array.length; i += chunkSize) {
    const chunk = array.slice(i, i + chunkSize);
    
    for (const item of chunk) {
      yield item;
    }
    
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

/**
 * Monitor memory usage at regular intervals
 * @param intervalMs Interval in milliseconds
 * @param thresholdPercentage Threshold percentage for high memory usage alerts
 * @returns Function to stop monitoring
 */
export function startMemoryMonitoring(
  intervalMs = 60000,
  thresholdPercentage = 85
): () => void {
  logger.info('Starting memory monitoring', {
    intervalMs,
    thresholdPercentage
  });
  
  const intervalId = setInterval(() => {
    const stats = getMemoryStats();
    
    logger.debug('Memory usage', {
      heapTotal: formatBytes(stats.heapTotal),
      heapUsed: formatBytes(stats.heapUsed),
      heapUsagePercentage: `${stats.heapUsagePercentage.toFixed(2)}%`,
    });
    
    if (stats.heapUsagePercentage > thresholdPercentage) {
      logger.warn('High memory usage detected', null, {
        heapUsagePercentage: `${stats.heapUsagePercentage.toFixed(2)}%`,
        heapUsed: formatBytes(stats.heapUsed),
        heapTotal: formatBytes(stats.heapTotal)
      });
    }
  }, intervalMs);
  
  // Return function to stop monitoring
  return () => {
    clearInterval(intervalId);
    logger.info('Stopped memory monitoring');
  };
}
