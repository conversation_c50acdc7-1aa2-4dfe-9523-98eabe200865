import { supabase, isSupabaseConfigured } from './supabase';
import { Token, TelegramMessage, TwitterTweet, BinanceAnnouncement, ScrapeResult } from './types';
import { Database } from './database.types';
import logger from './logger';
import { AppError, ErrorCategory } from './errorUtils';
import { withDb, PaginationParams, PaginatedResult, executePaginatedQuery } from './dbUtils';

// Types for Supabase row data
type SupabaseTokenRow = Database['public']['Tables']['tokens']['Row'];
type SupabaseTelegramRow = Database['public']['Tables']['telegram_messages']['Row'];
type SupabaseTwitterRow = Database['public']['Tables']['twitter_tweets']['Row'];
type SupabaseBinanceRow = Database['public']['Tables']['binance_announcements']['Row'];

// Types for Supabase insert data
type SupabaseTokenInsert = Database['public']['Tables']['tokens']['Insert'];
type SupabaseTelegramInsert = Database['public']['Tables']['telegram_messages']['Insert'];
type SupabaseTwitterInsert = Database['public']['Tables']['twitter_tweets']['Insert'];
type SupabaseBinanceInsert = Database['public']['Tables']['binance_announcements']['Insert'];

// Types for Supabase update data
type SupabaseTokenUpdate = Database['public']['Tables']['tokens']['Update'];

// Mapping functions
function mapSupabaseTokenToToken(supabaseToken: SupabaseTokenRow): Token {
  return {
    ticker: supabaseToken.ticker,
    source: supabaseToken.source,
    sourceUrl: supabaseToken.source_url || undefined,
    timestamp: supabaseToken.timestamp,
    verified: supabaseToken.verified || false,
    falsePositive: supabaseToken.false_positive || false,
    description: supabaseToken.description || undefined,
    marketCap: supabaseToken.market_cap || undefined,
    price: supabaseToken.price || undefined,
    priceChange24h: supabaseToken.price_change_24h || undefined,
    lastChecked: supabaseToken.last_checked || undefined
  };
}

function mapTokenToSupabaseToken(token: Token): SupabaseTokenInsert {
  return {
    ticker: token.ticker,
    source: token.source,
    source_url: token.sourceUrl || null,
    timestamp: token.timestamp,
    verified: token.verified || false,
    false_positive: token.falsePositive || false,
    description: token.description || null,
    market_cap: token.marketCap || null,
    price: token.price || null,
    price_change_24h: token.priceChange24h || null,
    last_checked: token.lastChecked || null
  };
}

function mapSupabaseTelegramToTelegram(supabaseTelegram: SupabaseTelegramRow): TelegramMessage {
  return {
    id: supabaseTelegram.message_id,
    text: supabaseTelegram.text,
    date: new Date(supabaseTelegram.date),
    url: supabaseTelegram.url || undefined
  };
}

function mapTelegramToSupabaseTelegram(telegram: TelegramMessage): SupabaseTelegramInsert {
  const dateString = telegram.date instanceof Date 
    ? telegram.date.toISOString() 
    : typeof telegram.date === 'string' 
    ? telegram.date 
    : new Date().toISOString();
    
  return {
    message_id: telegram.id,
    text: telegram.text,
    date: dateString,
    url: telegram.url || telegram.link || null
  };
}

function mapSupabaseTwitterToTwitter(supabaseTweet: SupabaseTwitterRow): TwitterTweet {
  return {
    id: supabaseTweet.tweet_id,
    text: supabaseTweet.text,
    created_at: supabaseTweet.created_at,
    createdAt: supabaseTweet.created_at,
    url: supabaseTweet.url
  };
}

function mapTwitterToSupabaseTwitter(tweet: TwitterTweet): SupabaseTwitterInsert {
  return {
    tweet_id: tweet.id,
    text: tweet.text,
    created_at: tweet.created_at || tweet.createdAt || new Date().toISOString(),
    url: tweet.url || tweet.link || `https://twitter.com/status/${tweet.id}`
  };
}

function mapSupabaseBinanceToBinance(supabaseBinance: SupabaseBinanceRow): BinanceAnnouncement {
  return {
    id: supabaseBinance.announcement_id,
    title: supabaseBinance.title,
    date: supabaseBinance.date,
    url: supabaseBinance.url,
    link: supabaseBinance.url
  };
}

function mapBinanceToSupabaseBinance(binance: BinanceAnnouncement): SupabaseBinanceInsert {
  return {
    announcement_id: binance.id,
    title: binance.title,
    date: binance.date,
    url: binance.url || binance.link || ''
  };
}

function mapPartialTokenToSupabaseUpdate(updates: Partial<Token>): SupabaseTokenUpdate {
  const result: SupabaseTokenUpdate = {};
  
  if (updates.ticker !== undefined) result.ticker = updates.ticker;
  if (updates.source !== undefined) result.source = updates.source;
  if (updates.sourceUrl !== undefined) result.source_url = updates.sourceUrl || null;
  if (updates.timestamp !== undefined) result.timestamp = updates.timestamp;
  if (updates.verified !== undefined) result.verified = updates.verified;
  if (updates.falsePositive !== undefined) result.false_positive = updates.falsePositive;
  if (updates.description !== undefined) result.description = updates.description || null;
  if (updates.marketCap !== undefined) result.market_cap = updates.marketCap || null;
  if (updates.price !== undefined) result.price = updates.price || null;
  if (updates.priceChange24h !== undefined) result.price_change_24h = updates.priceChange24h || null;
  if (updates.lastChecked !== undefined) result.last_checked = updates.lastChecked || null;
  
  return result;
}

// Token operations
export async function getTokens(): Promise<Token[]> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  return await withDb(async () => {
    const { data, error } = await supabase
      .from('tokens')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      logger.error('Error fetching tokens from Supabase', new Error(error.message), {
        code: error.code,
        details: error.details
      });
      throw new AppError(
        'Failed to fetch tokens from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_fetch_error',
        { service: 'supabase' }
      );
    }

    logger.info('Successfully fetched tokens from Supabase', { count: data?.length || 0 });
    return (data || []).map(mapSupabaseTokenToToken);
  });
}

/**
 * Get tokens with pagination support
 * @param params Pagination parameters
 * @returns Paginated result with tokens
 */
export async function getPaginatedTokens(params: PaginationParams = {}): Promise<PaginatedResult<Token>> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  return await withDb(async () => {
    // Create the base query
    const query = supabase
      .from('tokens')
      .select('*');

    // Create the count query
    const countQuery = supabase
      .from('tokens')
      .select('*', { count: 'exact', head: true });

    // Execute the paginated query
    const result = await executePaginatedQuery<SupabaseTokenRow>(query, countQuery, params);

    // Map the data to Token objects
    return {
      ...result,
      data: result.data.map(mapSupabaseTokenToToken)
    };
  });
}

export async function getTokenCount(): Promise<number> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  return await withDb(async () => {
    const { count, error } = await supabase
      .from('tokens')
      .select('*', { count: 'exact', head: true });

    if (error) {
      logger.error('Error counting tokens from Supabase', new Error(error.message), {
        code: error.code,
        details: error.details
      });
      throw new AppError(
        'Failed to count tokens from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_count_error',
        { service: 'supabase' }
      );
    }

    logger.debug('Successfully counted tokens from Supabase', { count });
    return count || 0;
  });
}

export async function saveToken(token: Token): Promise<void> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  // Check if token already exists
  const { data, error: fetchError } = await supabase
    .from('tokens')
    .select('id')
    .eq('ticker', token.ticker)
    .maybeSingle();

  if (fetchError) {
    logger.error('Error checking if token exists in Supabase', new Error(fetchError.message), {
      code: fetchError.code,
      details: fetchError.details,
      ticker: token.ticker
    });
    throw new AppError(
      'Failed to check if token exists in database',
      ErrorCategory.EXTERNAL_SERVICE,
      'db_fetch_error',
      { service: 'supabase', ticker: token.ticker }
    );
  }

  if (data) {
    // Update existing token
    const { error: updateError } = await supabase
      .from('tokens')
      .update(mapTokenToSupabaseToken(token))
      .eq('id', data.id);

    if (updateError) {
      logger.error('Error updating token in Supabase', new Error(updateError.message), {
        code: updateError.code,
        details: updateError.details,
        ticker: token.ticker
      });
      throw new AppError(
        'Failed to update token in database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_update_error',
        { service: 'supabase', ticker: token.ticker }
      );
    }
    logger.info('Successfully updated token in Supabase', { ticker: token.ticker });
  } else {
    // Insert new token
    const { error: insertError } = await supabase
      .from('tokens')
      .insert(mapTokenToSupabaseToken(token));

    if (insertError) {
      logger.error('Error inserting token to Supabase', new Error(insertError.message), {
        code: insertError.code,
        details: insertError.details,
        ticker: token.ticker
      });
      throw new AppError(
        'Failed to insert token into database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_insert_error',
        { service: 'supabase', ticker: token.ticker }
      );
    }
    logger.info('Successfully inserted token to Supabase', { ticker: token.ticker });
  }
}

export async function updateToken(ticker: string, updates: Partial<Token>): Promise<void> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  try {
    const { error } = await supabase
      .from('tokens')
      .update(mapPartialTokenToSupabaseUpdate(updates))
      .eq('ticker', ticker);

    if (error) {
      logger.error('Error updating token in Supabase', new Error(error.message), {
        code: error.code,
        details: error.details,
        ticker
      });
      throw new AppError(
        'Failed to update token in database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_update_error',
        { service: 'supabase', ticker }
      );
    }

    logger.info('Successfully updated token in Supabase', { ticker });
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Error updating token in Supabase', error instanceof Error ? error : new Error(String(error)), {
      ticker
    });
    throw new AppError(
      'Failed to update token in database',
      ErrorCategory.EXTERNAL_SERVICE,
      'db_update_error',
      { service: 'supabase', ticker }
    );
  }
}

// Scrape results operations
export async function getLatestScrapeResult(): Promise<ScrapeResult | null> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  try {
    // Get the latest scrape result
    const { data: scrapeData, error: scrapeError } = await supabase
      .from('scrape_results')
      .select('*')
      .order('fetched_at', { ascending: false })
      .limit(1)
      .single();

    if (scrapeError) {
      // Check if it's a "no rows returned" error, which is expected when no scrape results exist
      if (scrapeError.code === 'PGRST116' && scrapeError.details === 'The result contains 0 rows') {
        logger.info('No scrape results found in Supabase', {
          message: 'This is normal if no scrapes have been run yet'
        });
        return null;
      }

      logger.error('Error fetching latest scrape result from Supabase', new Error(scrapeError.message), {
        code: scrapeError.code,
        details: scrapeError.details
      });
      throw new AppError(
        'Failed to fetch scrape result from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_fetch_error',
        { service: 'supabase' }
      );
    }

    // If we have a valid scrape result, fetch the related data
    if (!scrapeData) {
      logger.warn('No scrape data found in Supabase despite successful query');
      return null;
    }

    try {
      // Get telegram messages
      const { data: telegramData, error: telegramError } = await supabase
        .from('telegram_messages')
        .select('*')
        .order('date', { ascending: false })
        .limit(10);

      if (telegramError) {
        logger.error('Error fetching telegram messages from Supabase', new Error(telegramError.message), {
          code: telegramError.code,
          details: telegramError.details
        });
        throw new AppError(
          'Failed to fetch telegram messages from database',
          ErrorCategory.EXTERNAL_SERVICE,
          'db_fetch_error',
          { service: 'supabase' }
        );
      }

      // Get twitter tweets
      const { data: twitterData, error: twitterError } = await supabase
        .from('twitter_tweets')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (twitterError) {
        logger.error('Error fetching twitter tweets from Supabase', new Error(twitterError.message), {
          code: twitterError.code,
          details: twitterError.details
        });
        throw new AppError(
          'Failed to fetch twitter tweets from database',
          ErrorCategory.EXTERNAL_SERVICE,
          'db_fetch_error',
          { service: 'supabase' }
        );
      }

      // Get binance announcements
      const { data: binanceData, error: binanceError } = await supabase
        .from('binance_announcements')
        .select('*')
        .order('date', { ascending: false })
        .limit(10);

      if (binanceError) {
        logger.error('Error fetching binance announcements from Supabase', new Error(binanceError.message), {
          code: binanceError.code,
          details: binanceError.details
        });
        throw new AppError(
          'Failed to fetch binance announcements from database',
          ErrorCategory.EXTERNAL_SERVICE,
          'db_fetch_error',
          { service: 'supabase' }
        );
      }

      // Get recently added tokens
      const { data: tokensData, error: tokensError } = await supabase
        .from('tokens')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(10);

      if (tokensError) {
        logger.error('Error fetching recently added tokens from Supabase', new Error(tokensError.message), {
          code: tokensError.code,
          details: tokensError.details
        });
        throw new AppError(
          'Failed to fetch tokens from database',
          ErrorCategory.EXTERNAL_SERVICE,
          'db_fetch_error',
          { service: 'supabase' }
        );
      }

      const result: ScrapeResult = {
        telegram: telegramData.map(mapSupabaseTelegramToTelegram),
        twitter: twitterData.map(mapSupabaseTwitterToTwitter),
        announcements: binanceData.map(mapSupabaseBinanceToBinance),
        addedTokens: tokensData.map(mapSupabaseTokenToToken),
        fetchedAt: scrapeData.fetched_at
      };

      logger.info('Successfully fetched scrape result from Supabase', {
        fetchedAt: result.fetchedAt,
        telegramCount: result.telegram.length,
        twitterCount: result.twitter.length,
        announcementsCount: result.announcements.length,
        tokensCount: result.addedTokens.length
      });

      return result;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }

      logger.error('Error processing scrape result data from Supabase', error instanceof Error ? error : new Error(String(error)));
      throw new AppError(
        'Failed to process scrape result data from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_processing_error',
        { service: 'supabase' }
      );
    }
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Error fetching latest scrape result from Supabase', error instanceof Error ? error : new Error(String(error)));
    throw new AppError(
      'Failed to fetch scrape result from database',
      ErrorCategory.EXTERNAL_SERVICE,
      'db_fetch_error',
      { service: 'supabase' }
    );
  }
}

export async function saveScrapeResult(result: ScrapeResult): Promise<void> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  try {
    // Insert scrape result metadata
    const { error: scrapeError } = await supabase
      .from('scrape_results')
      .insert({
        fetched_at: result.fetchedAt
      });

    if (scrapeError) {
      logger.error('Error inserting scrape result to Supabase', new Error(scrapeError.message), {
        code: scrapeError.code,
        details: scrapeError.details
      });
      throw new AppError(
        'Failed to insert scrape result into database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_insert_error',
        { service: 'supabase' }
      );
    }

    logger.info('Successfully inserted scrape result to Supabase', {
      fetchedAt: result.fetchedAt
    });

    // NOTE: Telegram messages, Twitter tweets, and Binance announcements 
    // are already saved by the history manager functions during deduplication.
    // We don't need to save them again here to avoid duplicate key violations.

    // Only save tokens if there are any new ones
    if (result.addedTokens.length > 0) {
      logger.info('Saving tokens to Supabase', { count: result.addedTokens.length });
      for (const token of result.addedTokens) {
        await saveToken(token);
      }
    }

    logger.info('Successfully saved scrape result metadata to database', {
      fetchedAt: result.fetchedAt,
      telegramCount: result.telegram.length,
      twitterCount: result.twitter.length,
      announcementsCount: result.announcements.length,
      addedTokensCount: result.addedTokens.length
    });
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Error saving scrape result to Supabase', error instanceof Error ? error : new Error(String(error)));
    throw new AppError(
      'Failed to save scrape result to database',
      ErrorCategory.EXTERNAL_SERVICE,
      'db_save_error',
      { service: 'supabase' }
    );
  }
}

// Telegram message operations
export async function getTelegramMessages(): Promise<TelegramMessage[]> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  return await withDb(async () => {
    const { data, error } = await supabase
      .from('telegram_messages')
      .select('*')
      .order('date', { ascending: false });

    if (error) {
      logger.error('Error fetching telegram messages from Supabase', new Error(error.message), {
        code: error.code,
        details: error.details
      });
      throw new AppError(
        'Failed to fetch telegram messages from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_fetch_error',
        { service: 'supabase' }
      );
    }

    logger.debug('Successfully fetched telegram messages from Supabase', { count: data?.length || 0 });
    return (data || []).map(mapSupabaseTelegramToTelegram);
  });
}

export async function saveTelegramMessages(messages: TelegramMessage[]): Promise<void> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  if (messages.length === 0) {
    return;
  }

  return await withDb(async () => {
    const { error } = await supabase
      .from('telegram_messages')
      .insert(messages.map(mapTelegramToSupabaseTelegram));

    if (error) {
      logger.error('Error inserting telegram messages to Supabase', new Error(error.message), {
        code: error.code,
        details: error.details,
        count: messages.length
      });
      throw new AppError(
        'Failed to insert telegram messages into database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_insert_error',
        { service: 'supabase' }
      );
    }

    logger.info('Successfully inserted telegram messages to Supabase', {
      count: messages.length
    });
  });
}

// Twitter tweet operations
export async function getTwitterTweets(): Promise<TwitterTweet[]> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  return await withDb(async () => {
    const { data, error } = await supabase
      .from('twitter_tweets')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('Error fetching twitter tweets from Supabase', new Error(error.message), {
        code: error.code,
        details: error.details
      });
      throw new AppError(
        'Failed to fetch twitter tweets from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_fetch_error',
        { service: 'supabase' }
      );
    }

    logger.debug('Successfully fetched twitter tweets from Supabase', { count: data?.length || 0 });
    return (data || []).map(mapSupabaseTwitterToTwitter);
  });
}

export async function saveTwitterTweets(tweets: TwitterTweet[]): Promise<void> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  if (tweets.length === 0) {
    return;
  }

  return await withDb(async () => {
    const { error } = await supabase
      .from('twitter_tweets')
      .insert(tweets.map(mapTwitterToSupabaseTwitter));

    if (error) {
      logger.error('Error inserting twitter tweets to Supabase', new Error(error.message), {
        code: error.code,
        details: error.details,
        count: tweets.length
      });
      throw new AppError(
        'Failed to insert twitter tweets into database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_insert_error',
        { service: 'supabase' }
      );
    }

    logger.info('Successfully inserted twitter tweets to Supabase', {
      count: tweets.length
    });
  });
}

// Binance announcement operations
export async function getBinanceAnnouncements(): Promise<BinanceAnnouncement[]> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  return await withDb(async () => {
    const { data, error } = await supabase
      .from('binance_announcements')
      .select('*')
      .order('date', { ascending: false });

    if (error) {
      logger.error('Error fetching binance announcements from Supabase', new Error(error.message), {
        code: error.code,
        details: error.details
      });
      throw new AppError(
        'Failed to fetch binance announcements from database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_fetch_error',
        { service: 'supabase' }
      );
    }

    logger.debug('Successfully fetched binance announcements from Supabase', { count: data?.length || 0 });
    return (data || []).map(mapSupabaseBinanceToBinance);
  });
}

export async function saveBinanceAnnouncements(announcements: BinanceAnnouncement[]): Promise<void> {
  if (!isSupabaseConfigured()) {
    logger.error('Supabase not configured', null, {
      action: 'Set required environment variables for Supabase'
    });
    throw new AppError(
      'Supabase not configured',
      ErrorCategory.VALIDATION,
      'supabase_not_configured',
      { action: 'Set required environment variables for Supabase' }
    );
  }

  if (announcements.length === 0) {
    return;
  }

  return await withDb(async () => {
    const { error } = await supabase
      .from('binance_announcements')
      .insert(announcements.map(mapBinanceToSupabaseBinance));

    if (error) {
      logger.error('Error inserting binance announcements to Supabase', new Error(error.message), {
        code: error.code,
        details: error.details,
        count: announcements.length
      });
      throw new AppError(
        'Failed to insert binance announcements into database',
        ErrorCategory.EXTERNAL_SERVICE,
        'db_insert_error',
        { service: 'supabase' }
      );
    }

    logger.info('Successfully inserted binance announcements to Supabase', {
      count: announcements.length
    });
  });
}

