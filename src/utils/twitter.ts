// Import TwitterTweet type only
import { TwitterTweet } from '@/lib/types';
import logger from '@/lib/logger';
import { withRetry, withCircuitBreaker } from '@/lib/retryUtils';
import { withCache } from '@/lib/cacheUtils';

// Initialize Twitter client
const initTwitterClient = () => {
  logger.info('Initializing Twitter client...');

  const bearerToken = process.env.TW_BEARER;

  if (!bearerToken) {
    logger.warn('Missing Twitter bearer token', null, {
      missingEnvVars: ['TW_BEARER'],
      action: 'Please set TW_BEARER in your .env file'
    });
    return null;
  }

  try {
    // Import <PERSON><PERSON>pi dynamically to avoid initialization issues
    const { Twitter<PERSON><PERSON> } = eval('require("twitter-api-v2")');

    // Create client with rate limit handling
    const client = new TwitterApi(bearerToken, {
      retry: true, // Enable automatic retries
      retryLimit: 3, // Maximum number of retries
      retryDelay: (retryCount: number) => 1000 * Math.pow(2, retryCount), // Exponential backoff
    });

    logger.info('Twitter client initialized successfully');
    return client.readOnly;
  } catch (error) {
    logger.error('Error initializing Twitter client', error instanceof Error ? error : new Error(String(error)));
    return null;
  }
};

// Internal function to get Twitter posts without caching
async function _getTwitterPosts(): Promise<TwitterTweet[]> {
  return withCircuitBreaker(
    'twitter-api',
    async () => {
      try {
        logger.info('Fetching Twitter posts...');
        const client = initTwitterClient();

        if (!client) {
          logger.warn('Twitter client not available', null, {
            reason: 'initialization_failed',
            action: 'Skipping Twitter posts'
          });
          return [];
        }

        const username = process.env.BINANCE_TWITTER || 'BinanceWallet';
        logger.info('Fetching tweets from user', { username });

        // First, get the user ID from the username with retry logic
        const user = await withRetry(
          async () => {
            logger.debug('Attempting to fetch Twitter user', { username });
            const result = await client.v2.userByUsername(username);

            if (!result.data) {
              throw new Error(`Twitter user ${username} not found`);
            }

            return result;
          },
          {
            maxRetries: 3,
            initialDelayMs: 1000,
            maxDelayMs: 5000,
            isRetryable: (error) => {
              // Only retry rate limit or network errors
              const errorStr = String(error).toLowerCase();
              const isRetryable =
                errorStr.includes('rate limit') ||
                errorStr.includes('network') ||
                errorStr.includes('timeout') ||
                errorStr.includes('connection') ||
                errorStr.includes('429'); // HTTP 429 Too Many Requests

              logger.debug('Checking if Twitter error is retryable', {
                isRetryable,
                errorMessage: errorStr.substring(0, 100)
              });

              return isRetryable;
            }
          }
        );

        const userId = user.data.id;
        logger.info('Found Twitter user', { username, userId });

        // Then, get the user's timeline with retry logic
        const timeline = await withRetry(
          async () => {
            logger.debug('Attempting to fetch Twitter timeline', { userId });
            return await client.v2.userTimeline(userId, {
              max_results: 5,
              'tweet.fields': ['created_at'],
            });
          },
          {
            maxRetries: 3,
            initialDelayMs: 1000,
            maxDelayMs: 5000,
            isRetryable: (error) => {
              // Only retry rate limit or network errors
              const errorStr = String(error).toLowerCase();
              const isRetryable =
                errorStr.includes('rate limit') ||
                errorStr.includes('network') ||
                errorStr.includes('timeout') ||
                errorStr.includes('connection') ||
                errorStr.includes('429'); // HTTP 429 Too Many Requests

              logger.debug('Checking if Twitter timeline error is retryable', {
                isRetryable,
                errorMessage: errorStr.substring(0, 100)
              });

              return isRetryable;
            }
          }
        );

        const tweets = timeline.data.data || [];
        logger.info('Retrieved tweets', { count: tweets.length });

        // Create a map to deduplicate tweets by ID
        const tweetMap = new Map();

        tweets.forEach((tweet: { id: string; text: string; created_at?: string }) => {
          if (!tweetMap.has(tweet.id)) {
            tweetMap.set(tweet.id, {
              id: tweet.id,
              text: tweet.text,
              created_at: tweet.created_at || new Date().toISOString(),
              url: `https://twitter.com/${username}/status/${tweet.id}`,
            });
          }
        });

        // Convert map to array
        const result = Array.from(tweetMap.values());
        logger.debug('Processed tweets', {
          rawCount: tweets.length,
          uniqueCount: result.length
        });

        return result;
      } catch (error) {
        logger.error('Error fetching Twitter posts',
          error instanceof Error ? error : new Error(String(error))
        );
        return [];
      }
    },
    {
      failureThreshold: 3,
      resetTimeoutMs: 60000, // 1 minute
      successThreshold: 2,
      isFailure: (error) => {
        // Determine if this error should trigger the circuit breaker
        const errorStr = String(error).toLowerCase();

        // Don't count authentication errors as circuit breaker failures
        const isAuthError =
          errorStr.includes('authentication') ||
          errorStr.includes('unauthorized') ||
          errorStr.includes('bearer token') ||
          errorStr.includes('401'); // HTTP 401 Unauthorized

        return !isAuthError;
      }
    }
  );
}

// Cached version of getTwitterPosts with 5-minute TTL
export const getTwitterPosts = withCache(_getTwitterPosts, {
  ttl: 5 * 60 * 1000, // 5 minutes
  staleWhileRevalidate: true
});
