import { BinanceAnnouncement } from '@/lib/types';
import logger from '@/lib/logger';
import { withRetry, withCircuitBreaker } from '@/lib/retryUtils';
import { withCache } from '@/lib/cacheUtils';

// Initialize Firecrawl client
const initFirecrawlClient = () => {
  logger.info('Initializing Firecrawl client...');

  const apiKey = process.env.FIRECRAWL_API_KEY;

  if (!apiKey) {
    logger.warn('Missing Firecrawl API key', null, {
      missingEnvVars: ['FIRECRAWL_API_KEY'],
      action: 'Please set FIRECRAWL_API_KEY in your .env file'
    });
    return null;
  }

  try {
    // Import FirecrawlApp
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const FirecrawlApp = require('@mendable/firecrawl-js').default;

    // Create a client using the FirecrawlApp class
    const client = new FirecrawlApp({
      apiKey,
      timeout: 30000, // 30 seconds timeout
    });

    logger.info('Firecrawl client initialized successfully');
    return client;
  } catch (error) {
    logger.error('Error initializing Firecrawl client', error instanceof Error ? error : new Error(String(error)));
    return null;
  }
};

// Internal function to get Binance announcements without caching
async function _getBinanceAnns(): Promise<BinanceAnnouncement[]> {
  return withCircuitBreaker(
    'binance-api',
    async () => {
      try {
        logger.info('Fetching Binance announcements...');
        const client = initFirecrawlClient();

        if (!client) {
          logger.warn('Firecrawl client not available', null, {
            reason: 'initialization_failed',
            action: 'Skipping Binance announcements'
          });
          return [];
        }

        const url = process.env.BINANCE_ANNOUNCEMENTS_URL || 'https://www.binance.com/en/support/announcement/list/48';
        logger.info('Scraping Binance announcements', { url });

        try {
          // Scrape the page using Firecrawl with retry logic
          const result = await withRetry(
            async () => {
              logger.debug('Attempting to scrape Binance page', { url });
              return await client.scrapeUrl(url, {
                formats: ['markdown'],
              });
            },
            {
              maxRetries: 3,
              initialDelayMs: 2000,
              maxDelayMs: 10000,
              isRetryable: (error) => {
                // Only retry certain types of errors
                const errorStr = String(error).toLowerCase();
                const isRetryable =
                  errorStr.includes('timeout') ||
                  errorStr.includes('network') ||
                  errorStr.includes('connection') ||
                  errorStr.includes('rate limit') ||
                  errorStr.includes('too many requests') ||
                  errorStr.includes('5xx'); // Server errors

                logger.debug('Checking if Binance scrape error is retryable', {
                  isRetryable,
                  errorMessage: errorStr.substring(0, 100)
                });

                return isRetryable;
              }
            }
          );

          logger.info('Binance page scraped successfully');

          // Extract announcement links and titles using regex
          // This is a simplified approach - in a real implementation, you might want to use a DOM parser
          const announcementRegex = /\[([^\]]+)\]\(([^)]+)\)/g;

          // Based on Firecrawl documentation, the result structure might vary
          // We'll try different possible locations for the markdown content
          let content = '';

          if (typeof result === 'object' && result !== null) {
            // Try different possible paths to get the markdown content
            if ('data' in result && result.data && typeof result.data === 'object' && 'markdown' in result.data) {
              content = result.data.markdown as string;
            } else if ('markdown' in result) {
              content = result.markdown as string;
            } else if ('content' in result) {
              content = result.content as string;
            }
          }

          logger.debug('Extracted content from Binance page', { contentLength: content.length });

          const matches = [...content.matchAll(announcementRegex)];
          logger.debug('Found announcement matches', { matchCount: matches.length });

          // Process all matches and create a map to deduplicate by URL
          const announcementMap = new Map();

          matches.forEach((match) => {
            const title = match[1];
            const url = match[2];

            // Use URL as the key for deduplication
            if (!announcementMap.has(url)) {
              // Generate a more unique ID based on URL hash
              const urlHash = url
                .split('')
                .reduce((acc, char) => (acc * 31 + char.charCodeAt(0)) & 0xFFFFFFFF, 0)
                .toString(16);

              announcementMap.set(url, {
                id: `binance-${urlHash}`,
                title,
                url,
                date: new Date().toISOString(), // Firecrawl might not provide dates, so we use current date
              });
            }
          });

          // Convert map to array and take only the first 5 announcements
          const announcements = Array.from(announcementMap.values()).slice(0, 5);

          logger.info('Extracted Binance announcements', { count: announcements.length });

          return announcements;
        } catch (scrapeError) {
          logger.error('Error scraping Binance page',
            scrapeError instanceof Error ? scrapeError : new Error(String(scrapeError)),
            { url }
          );
          return [];
        }
      } catch (error) {
        logger.error('Error fetching Binance announcements',
          error instanceof Error ? error : new Error(String(error))
        );
        return [];
      }
    },
    {
      failureThreshold: 3,
      resetTimeoutMs: 60000, // 1 minute
      successThreshold: 2,
      isFailure: (error) => {
        // Determine if this error should trigger the circuit breaker
        const errorStr = String(error).toLowerCase();

        // Don't count authentication errors as circuit breaker failures
        const isAuthError =
          errorStr.includes('api key') ||
          errorStr.includes('authentication') ||
          errorStr.includes('unauthorized');

        return !isAuthError;
      }
    }
  );
}

// Cached version of getBinanceAnns with 5-minute TTL
export const getBinanceAnns = withCache(_getBinanceAnns, {
  ttl: 5 * 60 * 1000, // 5 minutes
  staleWhileRevalidate: true
});
