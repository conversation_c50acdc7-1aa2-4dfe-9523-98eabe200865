# Binance Token Scraper

A Next.js application that automatically scrapes cryptocurrency token information from multiple sources (Telegram, Twitter, and Binance announcements) and uses AI to detect new token listings. The application provides a dashboard to view the latest scrape results and a dedicated token tracker to monitor all discovered tokens.

## Features

- Scrapes the 5 newest messages from Binance's official Telegram channel
- Scrapes the 5 newest tweets from BinanceWallet's Twitter account
- Scrapes the 5 newest announcements from Binance's "New Cryptocurrency Listing" page
- Uses Gemini-Flash (via OpenRouter + Vercel AI SDK) to detect new token tickers
- Deduplicates tokens against a local database
- Sends Telegram notifications when new tokens are discovered
- Runs hourly via Vercel cron jobs
- Displays results in a simple dashboard
- Provides a dedicated token tracker page

## Tech Stack

- **Next.js** - React framework
- **TypeScript** - Type safety
- **TailwindCSS** - Styling
- **GramJS** - Telegram MTProto client
- **twitter-api-v2** - Twitter API client
- **@mendable/firecrawl-js** - Web scraping
- **Vercel AI SDK** - AI runtime
- **OpenRouter** - AI provider
- **Gemini-2.5-Flash** - AI model
- **Supabase** - Database and storage

## Complete Setup Guide

### Prerequisites

- Node.js 18+ or Bun
- Telegram API credentials (API ID, API Hash, and Session String)
- Twitter API bearer token
- Firecrawl API key
- OpenRouter API key
- Telegram bot token and chat ID (for notifications)
- Supabase account and project (for database storage)

### Step 1: Clone the Repository

```bash
git clone https://github.com/yourusername/binance-token-scraper.git
cd binance-token-scraper
```

### Step 2: Install Dependencies

```bash
npm install
```

### Step 3: Set Up Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and fill in your API keys:
   ```
   TG_API_ID=your_telegram_api_id
   TG_API_HASH=your_telegram_api_hash
   TG_SESSION_STRING=your_telegram_session_string
   TW_BEARER=your_twitter_bearer_token
   FIRECRAWL_API_KEY=your_firecrawl_api_key
   OPENROUTER_API_KEY=your_openrouter_api_key
   BINANCE_CHANNEL=Binance_Announcements
   BINANCE_TWITTER=BinanceWallet
   BINANCE_ANNOUNCEMENTS_URL=https://www.binance.com/en/support/announcement/list/48
   NOTIFICATION_BOT_TOKEN=your_notification_bot_token
   NOTIFICATION_CHAT_ID=your_notification_chat_id

   # Supabase configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

#### How to Get API Keys

- **Telegram API ID and Hash**: Visit [my.telegram.org/apps](https://my.telegram.org/apps) and create a new application
- **Telegram Session String**: Use the included script to generate a session string:
- **Telegram Bot Token**: Create a bot using [@BotFather](https://t.me/BotFather) on Telegram
- **Telegram Chat ID**: Add your bot to a group or channel and get the chat ID (you can use [@username_to_id_bot](https://t.me/username_to_id_bot) or similar)
  ```bash
  # First, make sure you have your Telegram API ID and Hash in your .env file:
  # TG_API_ID=your_api_id
  # TG_API_HASH=your_api_hash

  # Install required dependencies
  npm install

  # Run the session generator script
  node scripts/generate-telegram-session.js

  # Follow the prompts:
  # 1. Enter your phone number in international format (e.g., +**********)
  # 2. Enter the verification code sent to your Telegram app
  # 3. If you have two-factor authentication enabled, enter your password

  # The script will output a session string - copy this to your .env file as:
  # TG_SESSION_STRING=your_session_string
  ```

  **Note**: The session string is sensitive information that grants access to your Telegram account. Never share it publicly or commit it to your repository.
- **Twitter Bearer Token**: Create a Twitter Developer account at [developer.twitter.com](https://developer.twitter.com) and create a project
- **Firecrawl API Key**: Sign up at [firecrawl.dev](https://firecrawl.dev) and get your API key
- **OpenRouter API Key**: Sign up at [openrouter.ai](https://openrouter.ai) and get your API key
- **Supabase URL and Service Role Key**:
  1. Sign up at [supabase.com](https://supabase.com) and create a new project
  2. Go to Project Settings > API
  3. Copy the "Project URL" as your `NEXT_PUBLIC_SUPABASE_URL`
  4. Under "Project API keys", copy the "service_role" key as your `SUPABASE_SERVICE_ROLE_KEY`
  5. **IMPORTANT**: The service role key has admin privileges. Never expose it in client-side code or commit it to your repository.

### Step 4: Set Up Supabase Database

#### Option 1: Using the SQL Editor (Manual Setup)

1. Log in to your Supabase dashboard and select your project
2. Go to the SQL Editor (left sidebar)
3. Create a new query
4. Copy and paste the entire contents of the `supabase_setup.sql` file into the SQL Editor
5. Click "Run" to execute the SQL commands

#### Option 2: Using the Setup Script (Automated Setup)

Run the provided setup script to automatically create all tables, indexes, and policies:

```bash
# Make sure you have your Supabase URL and service role key in your .env file
npx tsx scripts/setup-supabase.ts
```

#### Option 3: Manual SQL Execution

You can also run the SQL commands directly from the provided SQL file:

```bash
# First, make sure you have your Supabase URL and service role key in your .env file
# Open the supabase_setup.sql file and run the commands in your Supabase SQL Editor
```

#### Verify Your Setup

After setting up the database, verify that everything is working correctly:

```bash
npx tsx scripts/test-supabase-connection.ts
```

#### Migrate Existing Data (Optional)

If you have existing data in local JSON files, you can migrate it to Supabase:

```bash
npx tsx scripts/migrate-to-supabase.ts
```

For detailed information about the database schema, security policies, and troubleshooting, see:
- `SUPABASE_SETUP.md` - Comprehensive setup guide
- `SUPABASE_TROUBLESHOOTING.md` - Solutions for common issues

### Step 5: Create Local Data Directory (Fallback)

```bash
mkdir -p data
echo "[]" > data/tokens.json
```

### Step 6: Run the Development Server

```bash
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Step 7: Test the Scraper

1. Navigate to [http://localhost:3000](http://localhost:3000) in your browser
2. Click the "Run Scraper" button in the top right corner
3. Wait for the scraper to complete (this may take a few seconds)
4. Refresh the page to see the results

### Step 8: Test Telegram Notifications

1. Navigate to [http://localhost:3000/api/notify/test](http://localhost:3000/api/notify/test) in your browser
2. This will send a test notification to your Telegram bot
3. Check your Telegram channel or group to see if the notification was received

### Step 9: View Token Tracker

Click the "Token Tracker" button in the top right corner to view all discovered tokens.

## Production Deployment

### Deploying to Vercel

1. Push your code to a GitHub repository
2. Sign up for [Vercel](https://vercel.com) if you haven't already
3. Create a new project and import your GitHub repository
4. Add all your environment variables in the Vercel dashboard
5. Deploy the project

The cron job is configured in `vercel.json` to run hourly. Vercel will automatically trigger the `/api/scrape` endpoint every hour.

## Project Structure

```
binance-bot/
├── .env.example                    # Example environment variables
├── .env                            # Environment variables (gitignored)
├── data/                           # Data storage (fallback for local development)
│   ├── tokens.json                 # Database of known tokens
│   └── crypto_announcements.json   # Hourly scrape results
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   └── scrape/
│   │   │       └── route.ts        # API route for scraping (cron job target)
│   │   ├── tokens/
│   │   │   └── page.tsx            # Token tracker page
│   │   ├── page.tsx                # Main dashboard page
│   │   └── layout.tsx              # App layout
│   ├── components/
│   │   ├── Header.tsx              # Header component
│   │   ├── Footer.tsx              # Footer component
│   │   ├── TokenList.tsx           # Token list component
│   │   └── TokenActions.tsx        # Token actions component
│   ├── lib/
│   │   ├── utils.ts                # Utility functions
│   │   ├── types.ts                # TypeScript type definitions
│   │   ├── supabase.ts             # Supabase client
│   │   ├── database.types.ts       # Supabase database types
│   │   └── supabaseService.ts      # Supabase data service
│   └── utils/
│       ├── telegram.ts             # Telegram scraping functionality
│       ├── twitter.ts              # Twitter scraping functionality
│       ├── binance.ts              # Binance scraping functionality
│       ├── analyzeTokens.ts        # AI token analysis
│       └── notifyTelegram.ts       # Telegram notification functionality
├── scripts/
│   ├── generate-telegram-session.js # Script to generate Telegram session string
│   ├── migrate-to-supabase.ts      # Script to migrate data to Supabase
│   ├── setup-supabase.ts           # Script to set up Supabase database
│   └── test-supabase-connection.ts # Script to test Supabase connection
├── supabase_setup.sql              # SQL commands for Supabase setup
├── SUPABASE_SETUP.md               # Comprehensive Supabase setup guide
├── SUPABASE_TROUBLESHOOTING.md     # Supabase troubleshooting guide
├── vercel.json                     # Vercel configuration (cron jobs)
└── package.json                    # Project dependencies
```

## Troubleshooting

### Common Issues

1. **Telegram Authentication Errors**:
   - Make sure your API ID, API Hash, and Session String are correct
   - The Session String may expire after some time; generate a new one if needed

2. **Twitter API Errors**:
   - Ensure your bearer token has the correct permissions
   - Twitter API has rate limits; if you hit them, wait and try again

3. **Firecrawl Errors**:
   - Check if you've exceeded the free tier limits
   - Verify that the Binance announcements URL is still valid

4. **OpenRouter/AI Errors**:
   - Ensure your API key is valid
   - Check if you have sufficient credits for the Gemini model

5. **Supabase Errors**:
   - Verify that your Supabase URL and service role key are correct
   - Check if you've created all the required tables in your Supabase project
   - If you encounter permission errors, make sure you're using the service role key, not the anon key
   - Run the test script to verify your Supabase setup: `npx tsx scripts/test-supabase-connection.ts`
   - For detailed troubleshooting, refer to the `SUPABASE_TROUBLESHOOTING.md` file

6. **Library Import Errors**:
   - If you encounter import errors with Firecrawl or OpenRouter, check that you're using the correct import syntax:
     ```typescript
     // For Firecrawl
     import FirecrawlApp from '@mendable/firecrawl-js';

     // For OpenRouter
     import { openrouter } from '@openrouter/ai-sdk-provider';
     ```

   - When using Firecrawl, create a client and use the scrapeUrl method:
     ```typescript
     const client = new FirecrawlApp({ apiKey });
     const result = await client.scrapeUrl(url, { formats: ['markdown'] });
     ```

   - When using OpenRouter with the Vercel AI SDK, specify the model ID directly:
     ```typescript
     const ai = openrouter('google/gemini-2.5-flash-preview-05-20');
     const res = await generateText({
       model: ai,
       messages: [...]
     });
     ```

   - For Twitter API v2, use dynamic imports to avoid initialization errors:
     ```typescript
     // Instead of this (which can cause initialization errors):
     import { TwitterApi } from 'twitter-api-v2';

     // Use this approach:
     let TwitterApi: any;

     // Then in your function:
     if (!TwitterApi) {
       TwitterApi = require('twitter-api-v2').TwitterApi;
     }
     const client = new TwitterApi(bearerToken);
     ```

### Running Without API Keys

The application requires API keys to function properly, but you can still run the UI without them:

1. **Required API Keys**: For full functionality, you need to set up the following API keys in your `.env` file:
   - `TG_API_ID`, `TG_API_HASH`, and `TG_SESSION_STRING` for Telegram
   - `TW_BEARER` for Twitter
   - `FIRECRAWL_API_KEY` for Firecrawl
   - `NOTIFICATION_BOT_TOKEN` and `NOTIFICATION_CHAT_ID` for Telegram notifications

2. **Graceful Degradation**: The application will skip services where API keys are missing:
   - If Telegram credentials are missing, Telegram posts will be skipped
   - If Twitter bearer token is missing, Twitter posts will be skipped
   - If Firecrawl API key is missing, Binance announcements will be skipped
   - If notification credentials are missing, Telegram notifications will be skipped

3. **Testing the UI**: You can still test the UI without any API keys:
   ```bash
   # Create the data directory
   mkdir -p data
   # Create an empty tokens database
   echo "[]" > data/tokens.json
   # Run the application
   npm run dev
   ```

4. **Using Example Data**: If you prefer to see the UI with predefined data:
   ```bash
   mkdir -p data
   cp data/crypto_announcements.json.example data/crypto_announcements.json
   echo '["NEW", "SAMPLE", "TEST"]' > data/tokens.json
   ```

### Logs

The application logs detailed information to the console. Check the logs for error messages if something isn't working as expected.

### Error Handling

The application includes built-in error handling:

- Error boundaries to catch and display runtime errors
- Loading states for better user experience
- Detailed error messages with troubleshooting tips

## License

This project is licensed under the MIT License.

## Acknowledgements

- [Vercel](https://vercel.com) for hosting and cron jobs
- [OpenRouter](https://openrouter.ai) for AI model access
- [GramJS](https://gram.js.org) for Telegram API access
- [twitter-api-v2](https://github.com/PLhery/node-twitter-api-v2) for Twitter API access
- [Firecrawl](https://firecrawl.dev) for web scraping
- [Supabase](https://supabase.com) for database and storage
